import torch
from torch.optim import <PERSON><PERSON>
from torch.nn import CrossEntropyLoss
from transformers import get_scheduler
from sklearn.metrics import accuracy_score, f1_score  # Import for metrics

from .model.model_defination import load_gemma3n_model_for_classification, load_gemma3n_processor
from .data.dataloader import create_face_dataloaders
from .data.dataset import FaceCropDataset  # Import FaceCropDataset

# --- Configuration ---
# Ideally, these would come from a separate config.py file
model_name = "unsloth/gemma-3n-E2B-it"
num_labels = 2
lora_config_dict = {"r": 8, "lora_alpha": 16, "lora_dropout": 0.05, "bias": "none", "task_type": "SEQ_CLS"}
num_epochs = 3
batch_size = 32
learning_rate = 5e-5
gradient_accumulation_steps = 4
log_interval = 10  # Log every 10 batches
output_dir = "/home/<USER>/Documents/Anas/age_verification_gemma3n/models"  # Directory to save model checkpoints
manifest_file_train = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset/annotations_combined.json"
manifest_file_val = "path/to/your/val_manifest.json"
image_base_dir = "/home/<USER>/Documents/Anas/jmsc_age_detection/dataset"
age_threshold = 30  # Age threshold for classification

# --- Setup Device ---
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"Using device: {device}")

# --- Load Model and Processor ---
model = load_gemma3n_model_for_classification(model_name, num_labels, lora_config_dict).to(device)
processor = load_gemma3n_processor(model_name)

# --- Load Datasets and Create Dataloaders ---
train_dataset = FaceCropDataset(manifest_file=manifest_file_train, image_base_dir=image_base_dir, age_threshold=age_threshold)
val_dataset = FaceCropDataset(manifest_file=manifest_file_val, image_base_dir=image_base_dir, age_threshold=age_threshold)

train_loader = create_face_dataloaders(train_dataset, processor, batch_size=batch_size, shuffle=True)
val_loader = create_face_dataloaders(val_dataset, processor, batch_size=batch_size, shuffle=False)

# --- Optimizer ---
optimizer = AdamW(model.parameters(), lr=learning_rate)

# --- Loss Function ---
loss_fn = CrossEntropyLoss()

# --- Learning Rate Scheduler ---
num_training_steps = len(train_loader) * num_epochs
scheduler = get_scheduler(
    name="linear",
    optimizer=optimizer,
    num_warmup_steps=int(0.1 * num_training_steps),
    num_training_steps=num_training_steps,
)

# --- Training and Validation Loops ---
best_f1_score = -1.0

for epoch in range(num_epochs):
    # Training
    model.train()
    total_train_loss = 0
    for batch_idx, batch in enumerate(train_loader):
        inputs = {k: v.to(device) for k, v in batch.items() if k != "labels"}
        labels = batch["labels"].to(device)

        outputs = model(**inputs, labels=labels)
        loss = outputs.loss
        logits = outputs.logits

        loss.backward()
        total_train_loss += loss.item()

        if (batch_idx + 1) % gradient_accumulation_steps == 0:
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad() # Zero gradients after step

        if (batch_idx + 1) % log_interval == 0:
            print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx+1}/{len(train_loader)}, Train Loss: {loss.item():.4f}")

    avg_train_loss = total_train_loss / len(train_loader)
    print(f"Epoch {epoch+1} finished. Average Train Loss: {avg_train_loss:.4f}")

    # Validation
    model.eval()
    total_val_loss = 0
    all_preds = []
    all_labels = []
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            inputs = {k: v.to(device) for k, v in batch.items() if k != "labels"}
            labels = batch["labels"].to(device)

            outputs = model(**inputs, labels=labels)
            loss = outputs.loss
            logits = outputs.logits

            total_val_loss += loss.item()
            
            preds = torch.argmax(logits, dim=-1).cpu().numpy()
            all_preds.extend(preds)
            all_labels.extend(labels.cpu().numpy())

    avg_val_loss = total_val_loss / len(val_loader)
    accuracy = accuracy_score(all_labels, all_preds)
    f1 = f1_score(all_labels, all_preds, average='binary') # Assuming binary classification

    print(f"Validation Results - Epoch {epoch+1}:")
    print(f"  Average Val Loss: {avg_val_loss:.4f}")
    print(f"  Accuracy: {accuracy:.4f}")
    print(f"  F1 Score: {f1:.4f}")

    # Save best model checkpoint
    if f1 > best_f1_score:
        best_f1_score = f1
        # Create output directory if it doesn't exist
        import os
        os.makedirs(output_dir, exist_ok=True)
        model_save_path = os.path.join(output_dir, f"best_model_epoch_{epoch+1}_f1_{f1:.4f}")
        model.save_pretrained(model_save_path)
        print(f"Saved best model to {model_save_path} with F1 Score: {f1:.4f}")

print("\nTraining complete!")
